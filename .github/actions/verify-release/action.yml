name: 'Verify an NPM release'
description: 'Fetches a package from NPM and does some basic smoke tests'

inputs:
  npm-package:
    description: 'NPM Package'
    required: true
    default: '@google/gemini-cli@latest'
  expected-version:
    description: 'Expected version'
    required: true
  ref:
    description: 'The branch, tag, or SHA to release from.'
    required: false
    type: 'string'
    default: 'main'

runs:
  using: 'composite'
  steps:
    - name: '📝 Print Inputs'
      shell: 'bash'
      run: |
        echo "${{ toJSON(inputs) }}"
    - name: 'Checkout'
      uses: 'actions/checkout@08eba0b27e820071cde6df949e0beb9ba4906955' # ratchet:actions/checkout@v4
      with:
        ref: '${{ github.event.inputs.ref }}'
        fetch-depth: 0

    - name: 'Install from NPM'
      uses: 'nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08' # ratchet:nick-fields/retry@v3
      with:
        timeout_seconds: 900
        retry_wait_seconds: 30
        max_attempts: 10
        command: |-
          npm install --prefer-online --no-cache -g ${{ inputs.npm-package }}

    # This provides a very basic smoke test for Gemini CLI
    - name: 'Run Gemini CLI'
      id: 'gemini_cli'
      shell: 'bash'
      run: |-
        echo "gemini_version=$(gemini --version)" >> $GITHUB_OUTPUT

    # Force a failure if it doesn't match
    - name: 'Fail workflow if version does not match'
      if: '${{ steps.gemini_cli.outputs.gemini_version != inputs.expected-version }}'
      shell: 'bash'
      run: |-
        echo '❌ Got ${{ steps.gemini_cli.outputs.gemini_version }} from ${{ inputs.npm-package }}'
        echo '❌ Expected Version ${{ inputs.expected-version }}'

        exit 1
