name: 'Tag an NPM release'
description: 'Tags a specific npm version to a specific channel.'

inputs:
  channel:
    description: 'NPM Channel tag'
    required: true
  version:
    description: 'version'
    required: true
  dry-run:
    description: 'Whether to run in dry-run mode.'
    required: true
  wombat-token-core:
    description: 'The npm token for the wombat @google/gemini-cli-core'
    required: true
  wombat-token-cli:
    description: 'The npm token for wombat @google/gemini-cli'

runs:
  using: 'composite'
  steps:
    - name: 'Setup Node.js'
      uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
      with:
        node-version-file: '.nvmrc'
        registry-url: 'https://wombat-dressing-room.appspot.com'
        scope: '@google'

    - name: 'Change tag for @google/gemini-cli-core'
      if: |-
        ${{ inputs.dry-run == 'false' }}
      env:
        NODE_AUTH_TOKEN: '${{ inputs.wombat-token-core }}'
      shell: 'bash'
      run: |
        npm dist-tag add @google/gemini-cli-core@${{ inputs.version }} ${{ inputs.channel }}

    - name: 'Change tag for @google/gemini-cli'
      if: |-
        ${{ inputs.dry-run == 'false' }}
      env:
        NODE_AUTH_TOKEN: '${{ inputs.wombat-token-cli }}'
      shell: 'bash'
      run: |
        npm dist-tag add @google/gemini-cli@${{ inputs.version }} ${{ inputs.channel }}

    - name: 'Log dry run'
      if: |-
        ${{ inputs.dry-run == 'true' }}
      shell: 'bash'
      run: |
        echo "Dry run: Would have added tag '${{ inputs.channel }}' to version '${{ inputs.version }}' for @google/gemini-cli and @google/gemini-cli-core."
